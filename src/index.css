@import "tailwindcss";
@import "tailwindcss-animated";
@import "./style/themes.css";

@plugin "daisyui" {
  themes:
    [ "light",
    "dark"];
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  html,
  body {
    font-family: "Noto Sans Thai", sans-serif;
  }

  /* Typography Styles */
  .text-h1 {
    font-size: 28px;
    line-height: 36px;
    font-weight: 600;
  }

  .text-h2 {
    font-size: 24px;
    line-height: 30px;
    font-weight: 600;
  }

  .text-h3 {
    font-size: 20px;
    line-height: 26px;
    font-weight: 600;
  }

  .text-h4 {
    font-size: 18px;
    line-height: 24px;
    font-weight: 600;
  }

  .text-h5 {
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
  }

  .text-h6 {
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
  }

  .text-body-base {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
  }

  .text-body-sm {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
  }

  .text-body-xs {
    font-size: 12px;
    line-height: 18px;
    font-weight: 400;
  }

  .text-label-sm {
    font-size: 13px;
    line-height: 20px;
    font-weight: 600;
  }

  .text-label-xs {
    font-size: 11px;
    line-height: 16px;
    font-weight: 600;
  }
}

html,
body,
#root {
  height: 100%;
}

.btn {
  border-radius: 0.8rem;
}
