import { cn } from "@utils/cn";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  icon?: React.ReactNode;
}

export const Input = ({ className, icon, ...props }: InputProps) => {
  return (
    <label className="input input-primary rounded-lg border-none bg-base-200">
      {icon}
      <input
        required
        {...props}
        className={cn("text-body-xs placeholder:text-color-neutral", className)}
      />
    </label>
  );
};
