// components/layout/Navigation.tsx

import logo from "@assets/logo.png";
import { Button } from "@components/common/button";
import { Input } from "@components/common/input";
import { LanguageToggleButton } from "@components/languageToggleButton";
import { faMagnifyingGlass, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";

export const Header = () => {
  const { t } = useTranslation();

  return (
    <div className="navbar flex justify-between bg-base-content px-6 py-2 shadow-sm">
      <img
        src={logo}
        alt="DinoDesk Logo"
        className="mask mask-circle h-10 w-10"
      />
      <Input
        icon={<FontAwesomeIcon icon={faMagnifyingGlass} size="lg" />}
        type="search"
        placeholder={t("navigation.nameAndPhone")}
      />
      <div className="flex items-center gap-4">
        <Button
          className="border-info/50 text-base-300 hover:text-base-content"
          variant="outline"
        >
          <FontAwesomeIcon icon={faPlus} />
          {t("navigation.addLead")}
        </Button>
        <div className="drawer drawer-end">
          <input id="my-drawer-4" type="checkbox" className="drawer-toggle" />
          <div className="drawer-content">
            {/* Page content here */}
            <label
              htmlFor="my-drawer-4"
              className="drawer-button border-info/50 border-base-100 rounded-lg text-base-300 p-2 hover:bg-secondary-content hover:text-base-content"
            >
              {/* <FontAwesomeIcon icon={faPlus} />
              {t("navigation.addLead")} */}
              <Button
                className="border-info/50 text-base-300 hover:text-base-content"
                variant="outline"
              >
                <FontAwesomeIcon icon={faPlus} />
                {t("navigation.addLead")}
              </Button>
            </label>
          </div>
          <div className="drawer-side">
            <label
              htmlFor="my-drawer-4"
              aria-label="close sidebar"
              className="drawer-overlay"
            />
            <ul className="menu bg-base-200 text-base-content min-h-full w-80 p-4">
              {/* Sidebar content here */}
            </ul>
          </div>
        </div>
        <LanguageToggleButton />
      </div>
    </div>
  );
};
